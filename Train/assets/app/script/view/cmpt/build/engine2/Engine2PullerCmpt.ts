import StateObj from "../../../../model/passenger/StateObj";
import Engine2PullerObj, { Engine2PullerObjState } from "../../../../model/train/engine2/Engine2PullerObj";
import BuildQuietCmpt from "../BuildQuietCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Engine2PullerCmpt extends BuildQuietCmpt {
    @property(sp.Skeleton)
    private mask: sp.Skeleton = null

    public model: Engine2PullerObj = null
    private preState: StateObj<Engine2PullerObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        if (type == Engine2PullerObjState.ON) {
            sk.playAnimation("aniIdle", true)
            this.mask.node.active = true
            this.mask.playAnimation('aniIdle2', true)
        } else if (type == Engine2PullerObjState.OFF) {
            sk.playAnimation("jingzhi")
            this.mask.node.active = false
        } else {
            sk.playAnimation("jingzhi", true)
            this.mask.node.active = false
        }
    }
}
