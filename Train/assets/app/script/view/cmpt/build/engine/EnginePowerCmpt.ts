import { MAX_ZINDEX } from "../../../../common/constant/Constant";
import { BuildAnimation } from "../../../../common/constant/Enums";
import { StateType } from "../../../../model/passenger/StateEnum";
import StateObj from "../../../../model/passenger/StateObj";
import EnginePowerObj, { EnginePowerObjState } from "../../../../model/train/engine/EnginePowerObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnginePowerCmpt extends BuildCmpt {
    @property(sp.Skeleton)
    private body: sp.Skeleton = null
    @property(cc.Node)
    private lizi: cc.Node = null
    @property(sp.Skeleton)
    private bullet: sp.Skeleton = null

    public model: EnginePowerObj = null
    private preState: StateObj<EnginePowerObjState> = null

    protected initEditView() {
        this.body.playAnimation(BuildAnimation.JINGZHI)
        this.bullet.node.active = false
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        this.lizi.active = false
        switch (type) {
            case EnginePowerObjState.IDLE:
                this.playIdle(data)
                break
            case EnginePowerObjState.START:
                this.playStart(data)
                break
            case EnginePowerObjState.WORK:
                this.playWork(data)
                break
            case EnginePowerObjState.OUTPUT:
                this.playOutput(data)
                break
            case EnginePowerObjState.HAPPY_START:
                this.playHappyStart(data)
                break
            case EnginePowerObjState.HAPPY_END:
                this.playHappyEnd(data)
                break
            case EnginePowerObjState.ANGRY_START:
                this.playAngryStart(data)
                break
            case EnginePowerObjState.ANGRY_END:
                this.playAngryEnd(data)
                break
            default:
                this.body.playAnimation(BuildAnimation.IDLE)
        }
    }

    private async playHappyStart(data) { this.body.playAnimation(data.anim) }
    private async playHappyEnd(data) {
        this.body.setEndListener(null)
        this.body.setEventListener(({ animation }, { data }) => {
            if (data.name != "effect") return
            this.tryShoot(1)
        })
        this.body.playAnimation(data.anim)
    }

    private async playAngryStart(data) { this.body.playAnimation(data.anim) }
    private async playAngryEnd(data) {
        this.body.setEndListener(null)
        this.body.setEventListener(({ animation }, { data }) => {
            if (data.name != "effect") return
            this.tryShoot(2)
        })
        this.body.playAnimation(data.anim)
    }

    private async tryShoot(index: number) {
        const root = this.node.parent
        let roles = this.model.carriage?.getPassengers() || []
        roles = roles.filter(role => role.actionAgent?.getState(StateType.IDLE))
        const flyPos: { pos: cc.Vec2, it: cc.Node }[] = []
        const bulletPos = ut.convertToNodeAR(this.bullet.node, root)
        for (const role of roles) {
            const cmpt = role.view.getComponent("PassengerView")
            const pos = role.view.parent == root ? role.view.getPosition() : ut.convertToNodeAR(role.view, root)
            const it = cc.instantiate2(this.bullet.node, root)
            it.setPosition(bulletPos)
            it.zIndex = MAX_ZINDEX
            flyPos.push({ pos, it })
        }
        const promises = []
        let speed = 1300
        for (const { pos, it } of flyPos) {
            it.active = true

            import { MAX_ZINDEX } from "../../../../common/constant/Constant";
import { BuildAnimation } from "../../../../common/constant/Enums";
import { StateType } from "../../../../model/passenger/StateEnum";
import StateObj from "../../../../model/passenger/StateObj";
import EnginePowerObj, { EnginePowerObjState } from "../../../../model/train/engine/EnginePowerObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnginePowerCmpt extends BuildCmpt {
    @property(sp.Skeleton)
    private body: sp.Skeleton = null
    @property(cc.Node)
    private lizi: cc.Node = null
    @property(sp.Skeleton)
    private bullet: sp.Skeleton = null

    public model: EnginePowerObj = null
    private preState: StateObj<EnginePowerObjState> = null

    protected initEditView() {
        this.body.playAnimation(BuildAnimation.JINGZHI)
        this.bullet.node.active = false
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        this.lizi.active = false
        switch (type) {
            case EnginePowerObjState.IDLE:
                this.playIdle(data)
                break
            case EnginePowerObjState.START:
                this.playStart(data)
                break
            case EnginePowerObjState.WORK:
                this.playWork(data)
                break
            case EnginePowerObjState.OUTPUT:
                this.playOutput(data)
                break
            case EnginePowerObjState.HAPPY_START:
                this.playHappyStart(data)
                break
            case EnginePowerObjState.HAPPY_END:
                this.playHappyEnd(data)
                break
            case EnginePowerObjState.ANGRY_START:
                this.playAngryStart(data)
                break
            case EnginePowerObjState.ANGRY_END:
                this.playAngryEnd(data)
                break
            default:
                this.body.playAnimation(BuildAnimation.IDLE)
        }
    }

    private async playHappyStart(data) { this.body.playAnimation(data.anim) }
    private async playHappyEnd(data) {
        this.body.setEndListener(null)
        this.body.setEventListener(({ animation }, { data }) => {
            if (data.name != "effect") return
            this.tryShoot(1)
        })
        this.body.playAnimation(data.anim)
    }

    private async playAngryStart(data) { this.body.playAnimation(data.anim) }
    private async playAngryEnd(data) {
        this.body.setEndListener(null)
        this.body.setEventListener(({ animation }, { data }) => {
            if (data.name != "effect") return
            this.tryShoot(2)
        })
        this.body.playAnimation(data.anim)
    }

    private async tryShoot(index: number) {
        const root = this.node.parent
        let roles = this.model.carriage?.getPassengers() || []
        roles = roles.filter(role => role.actionAgent?.getState(StateType.IDLE))
        const flyPos: { pos: cc.Vec2, it: cc.Node }[] = []
        const bulletPos = ut.convertToNodeAR(this.bullet.node, root)
        for (const role of roles) {
            const cmpt = role.view.getComponent("PassengerView")
            const pos = role.view.parent == root ? role.view.getPosition() : ut.convertToNodeAR(role.view, root)
            const it = cc.instantiate2(this.bullet.node, root)
            it.setPosition(bulletPos)
            it.zIndex = MAX_ZINDEX
            flyPos.push({ pos, it })
        }
        const promises = []
        let speed = 1300
        for (const { pos, it } of flyPos) {
            it.active = true

            const direction = pos.sub(bulletPos)
            const angle = Math.atan2(direction.y, direction.x)
            const angleDegrees = angle * 180 / Math.PI
            it.angle = angleDegrees

            // 子弹头基于锚点的偏移量
            const bulletHeadOffset = 200
            const offsetX = bulletHeadOffset * Math.cos(angle)
            const offsetY = bulletHeadOffset * Math.sin(angle)
            const adjustedTargetX = pos.x - offsetX
            const adjustedTargetY = pos.y + 100 - offsetY

            const dis = pos.sub(bulletPos).mag()
            const time = dis / speed

            const sk = it.Component(sp.Skeleton)
            promises.push((async () => {
                await sk.playAnimation("enter" + index, false)
                sk.playAnimation("loop" + index, true)
                await cc.tween(it)
                    .to(time, { x: adjustedTargetX, y: adjustedTargetY })
                    .start()
                    .promise()
                await sk.playAnimation("impact")
                it.active = false
                it.destroy()
            })())
        }
        await Promise.all(promises)
    }

    private async playIdle(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed, true)
    }

    private async playStart(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed)
    }

    private async playWork(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed, true)

        let surplusTime = timeData.getSurplusTime() - 1
        if (surplusTime > 0) {
            this.lizi.active = true
            this.lizi.Component(cc.ParticleSystem).resetSystem()
            await ut.wait(surplusTime, this)
            let state = this.model.state
            if (state?.type == EnginePowerObjState.WORK) {
                this.lizi.Component(cc.ParticleSystem).stopSystem()
            }
        }
    }

    private async playOutput(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed)
    }
}


            // 子弹头基于锚点的偏移量
            const bulletHeadOffset = 200
            const offsetX = bulletHeadOffset * Math.cos(angle)
            const offsetY = bulletHeadOffset * Math.sin(angle)
            const adjustedTargetX = pos.x - offsetX
            const adjustedTargetY = pos.y + 100 - offsetY

            const dis = pos.sub(bulletPos).mag()
            const time = dis / speed

            const sk = it.Component(sp.Skeleton)
            promises.push((async () => {
                await sk.playAnimation("enter" + index, false)
                sk.playAnimation("loop" + index, true)
                await cc.tween(it)
                    .to(time, { x: adjustedTargetX, y: adjustedTargetY })
                    .start()
                    .promise()
                await sk.playAnimation("impact")
                it.active = false
                it.destroy()
            })())
        }
        await Promise.all(promises)
    }

    private async playIdle(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed, true)
    }

    private async playStart(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed)
    }

    private async playWork(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed, true)

        let surplusTime = timeData.getSurplusTime() - 1
        if (surplusTime > 0) {
            this.lizi.active = true
            this.lizi.Component(cc.ParticleSystem).resetSystem()
            await ut.wait(surplusTime, this)
            let state = this.model.state
            if (state?.type == EnginePowerObjState.WORK) {
                this.lizi.Component(cc.ParticleSystem).stopSystem()
            }
        }
    }

    private async playOutput(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed)
    }
}
