import { THROW_FUEL_SPEED } from "../../../common/constant/Constant";
import { EntrustLifeType, PassengerLifeAnimation } from "../../../common/constant/Enums";
import EngineModel from "../../train/engine/EngineModel";
import { EnginePowerObjState } from "../../train/engine/EnginePowerObj";
import { ActionNode } from "../ActionTree";
import { TimeStateData } from "../StateDataType";
import { StateType } from "../StateEnum";
import { ActionCfg } from "./ActionCfg";
import BaseAction from "./BaseAction";

export default class EngineAction extends BaseAction {
    protected carriage: EngineModel = null

    protected async start(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return

        let power = this.carriage.getPower()
        let state = power?.state
        let type = state?.type
        // if (type == EnginePowerObjState.IDLE) {
        //     if (this.checkAddFuel()) {
        //         await action.run(this.addFuel)
        //         await action.wait(ut.randomRange(1, 2))
        //     }
        //     else {
        //         await action.wait(1)
        //     }
        //     return
        // }
        // else if (type == EnginePowerObjState.WORK || type == EnginePowerObjState.START) {
        //     if (this.checkPlay()) {
        //         await action.run(this.toBuildPlay)
        //     }
        // }

        let cfgs = [
            { act: this.idle, weight: 25 }, //站立表演
            // { act: this.toRandomPos, weight: 25 }, //闲逛
        ]
        await this.runRandomAct(action, cfgs)
    }

    private checkPlay() {
        return this.checkPull() || this.checkComputerWork() || this.checkTreadmillPlay()
    }

    private checkTreadmillPlay() {
        return this.checkBuildPlay(this.carriage.getTreadmill())
    }

    private checkPull() {
        if (!this.actionAgent.getAnim(PassengerLifeAnimation.ENGINE_WORK)) {
            return
        }
        if (!this.checkBuildPlay(this.carriage.getPuller())) {
            return
        }
        return true
    }

    private checkComputerWork() {
        let build = this.carriage.getComputer()
        let chair = this.carriage.getChair()
        return this.checkBuildPlay(build) && this.checkBuildPlay(chair) && this.actionAgent.getAnim(PassengerLifeAnimation.ENGINE_COMPUTER)
    }

    private checkAddFuel() {
        let power = this.carriage.getPower()
        let state = power?.state
        if (state?.type != EnginePowerObjState.IDLE) return
        let timeData: TimeStateData = state.data.timeData
        let surplusTime = timeData.getSurplusTime()

        let anim = PassengerLifeAnimation.ENGINE_THROW
        if (!this.actionAgent.getAnim(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }
        let throwTime = this.getThrowFuelTime()
        return surplusTime > throwTime + this.actionAgent.getAnimTime(anim)
    }

    private async toBuildPlay(action) {
        let cfgs = [
            { act: this.toTreadmill, check: this.checkTreadmillPlay, build: this.carriage.getTreadmill() , weight: 100 }, //去跑步机
            { act: this.toPuller, check: this.checkPull, build: this.carriage.getPuller(), weight: 100 }, //去拉力器
            { act: this.toComputerWork, check: this.checkComputerWork, build: this.carriage.getComputer(), weight: 100 }, //去电脑工作
        ]

        cfgs = cfgs.filter(({ check }) => {
            if (check) return check.call(this)
            return true
        })
        if (cfgs.length > 0) {
            let pos = this.role.getPosition()
            let cfg = cfgs.min(cfg => cfg.build.position.sub(pos).magSqr())
            await action.run(cfg.act)
        }
        action.ok()
    }

    protected async addFuel(action: ActionNode) {
        this.debug("addFuel")
        let { build } = action.params || {}
        let anim = PassengerLifeAnimation.ENGINE_THROW
        if (!this.actionAgent.getAnim(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }
        let mountPoint = "shaozi_guadian"
        let actionAgent = this.actionAgent
        let power = this.carriage.getPower()
        let targetPos = power.getUsePos()
        let x = targetPos.x + ut.randomRange(-30, 30)
        let y = targetPos.y + ut.randomRange(-10, 10)
        targetPos = cc.v2(x, y)
        let throwTime = this.getThrowFuelTime()
        let time = this.actionAgent.getAnimTime(anim) + throwTime
        let type = StateType.ENGINE_ADD_FUEL
        let timeData = new TimeStateData().init(time)
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        this.role.setDirToPos(targetPos)
        let fuelType = ut.random(0, 2)
        actionAgent.pushState(type, { build, timeData, anim, mountPoint, targetPos, throwTime, fuelType })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private getThrowFuelTime() {
        let power = this.carriage.getPower()
        let targetPos = power.getUsePos()
        return this.role.getPosition().sub(targetPos).mag() / THROW_FUEL_SPEED
    }

    private async toTreadmill(action) {
        let build = this.carriage.getTreadmill()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.walk, { build })
        // this.actionAgent.addUseBuildRecord(build, index)
        action.onTerminate()
        action.ok()
    }

    protected async walk(action) {
        this.debug("walk")
        let { time, build, anim, mountPoint } = action.params || {}
        let buildSynchronize = this.carriage.getEnergy()
        let actionAgent = this.actionAgent
        let type = StateType.WALK_TREADMILL
        build = build || this.carriage.getTreadmill()
        anim = anim || PassengerLifeAnimation.WALK

        // time = time || this.getTimeByCfg(type, anim)
        await this.waitStartToWork(action)
        time = this.clampWorkAnimTime(anim)
        if (time <= 0) {
            return action.ok()
        }

        let timeData = new TimeStateData().init(time)
        action.onTerminate = () => {
            actionAgent.popState(type)
            build.onExit(this.role)
            buildSynchronize.onExit(this.role)
        }
        build.onEnter(this.role)
        buildSynchronize.onEnter(this.role)
        actionAgent.pushState(type, { build, timeData, anim })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //去拉力器
    protected async toPuller(action) {
        let params = action.params || {}
        let build = this.carriage.getPuller()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.pull, Object.assign(params, { build }))
        // this.actionAgent.addUseBuildRecord(build, index)
        action.onTerminate()
        action.ok()
    }

    protected async pull(action) {
        let { time, build, anim, mountPoint } = action.params || {}
        this.debug("pull")
        anim = anim || PassengerLifeAnimation.ENGINE_WORK
        build = build || this.carriage.getPuller()

        await this.waitStartToWork(action)
        time = this.clampWorkAnimTime(anim)
        if (time <= 0) {
            return action.ok()
        }

        let role = this.role
        build.onEnter(role)
        let actionAgent = this.actionAgent
        let type = StateType.ENGINE_PULL
        action.onTerminate = () => {
            build.onExit(role)
            actionAgent.popState(type)
        }
        // time = time || this.getTimeByCfg(type, anim)
    
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { build, timeData, anim, mountPoint })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }


    //去电脑桌工作
    protected async toComputerWork(action) {
        let params = action.params || {}
        let build = this.carriage.getChair()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        let minIndex = [1, 2].min(index => this.role.getPosition().sub(build.getUsePos(index)).magSqr())
        await action.run(this.moveToBuild, { build, paths: [{ index: minIndex }, { index, force: true }] })
        await action.run(this.computerWork, Object.assign(params, { build }))
        await action.run(this.forceMove, build.getUsePos(minIndex))
        action.onTerminate()
        // this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    //电脑工作
    protected async computerWork(action) {
        this.debug("computer_work")
        let { build, anim, mountPoint } = action.params || {}
        let computer = this.carriage.getComputer()
        let role = this.role
        anim = anim || PassengerLifeAnimation.ENGINE_COMPUTER

        await this.waitStartToWork(action)
        let time = this.clampWorkAnimTime(anim)
        if (time <= 0) {
            return action.ok()
        }

        computer.onEnter(role)
        build.onEnter(role)
        let actionAgent = this.actionAgent
        let type = StateType.ENGINE_COMPUTER
        action.onTerminate = () => {
            computer.onExit(role)
            build.onExit(role)
            actionAgent.popState(StateType.SIT)
            actionAgent.popState(type)
        }
        let totTime = 0
        let animTimes = []
        // let cfg = ActionCfg[type]
        // let times = this.randomCount(cfg.times)
        // for (let i = 0; i < times; i++) {
        //     let sleepTime = this.randomLoopAnimTime(cfg.sleep, PassengerLifeAnimation.ENGINE_SIT_SLEEP)
        //     let workTime = this.randomLoopAnimTime(cfg.work, PassengerLifeAnimation.ENGINE_WORK)
        //     animTimes.push({ sleepTime, workTime })
        //     totTime += sleepTime + workTime
        // }
        let timeData = new TimeStateData().init(time)
        actionAgent.pushState(StateType.SIT, { build, mountPoint })
        actionAgent.pushState(type, { build, timeData, animTimes, anim })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }


    private async waitStartToWork(action) {
        let power = this.carriage.getPower()
        let state = power.state
        let type = state?.type
        if (type == EnginePowerObjState.START) {
            await action.wait(state.data.timeData.getSurplusTime() + 0.1)
        }
    }

    private clampWorkAnimTime(anim) {
        let power = this.carriage.getPower()
        let surplusTime = power.getWorkSurplusTime()
        let animTime = this.actionAgent.getAnimTime(anim)
        let time = Math.ceil(surplusTime / animTime) * animTime
        // this.debugE("clampWorkAnimTime fail", surplusTime, time, anim, animTime)
        return time
    }
}