import CarriageModel from "../common/CarriageModel";
import { Engine2BuildType } from "../../../common/constant/Enums";
import Engine2PowerObj from "./Engine2PowerObj";
import Engine2PullerObj from "./Engine2PullerObj";
/**
 * 动力室
 */
export default class Engine2Model extends CarriageModel {

    public init(data) {
        super.init(data)
        return this
    }

    public getPower() {
        return this.getBuildByOrder(Engine2BuildType.POWER) as Engine2PowerObj
    }

    public getPuller() {
        return this.getBuildByOrder(Engine2BuildType.PULLER) as Engine2PullerObj
    }

    public getWaterBox() { return null }


    public newBuildObj(type: Engine2BuildType) {
        switch (type) {
            case Engine2BuildType.POWER: return new Engine2PowerObj()
            case Engine2BuildType.PULLER: return new Engine2PullerObj()
            default:
                return super.newBuildObj(type)
        }
    }

}