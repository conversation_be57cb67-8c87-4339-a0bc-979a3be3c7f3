import PassengerModel from "../../passenger/PassengerModel";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";
import Engine2Model from "./Engine2Model";

export enum Engine2PullerObjState {
    ON,
    OFF,
}

export default class Engine2PullerObj extends BuildObj {

    public state: StateObj<Engine2PullerObjState> = null

    public onEnter(role: PassengerModel) {
        super.onEnter(role)
        this.setState(Engine2PullerObjState.ON)

        let carriage = this.carriage as Engine2Model
        let waterBox = carriage.getWaterBox()
        waterBox?.on()
    }

    public onExit(role: PassengerModel): void {
        super.onExit(role)
        this.setState(Engine2PullerObjState.OFF)

        let carriage = this.carriage as Engine2Model
        let waterBox = carriage.getWaterBox()
        waterBox?.off()
    }

    private setState(type?, data?) {
        this.state = new StateObj<Engine2PullerObjState>().init(type, data)
    }

}