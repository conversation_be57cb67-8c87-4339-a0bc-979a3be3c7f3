import { PassengerAction } from "../../../common/constant/Enums";
import { gameHelper } from "../../../common/helper/GameHelper";
import BuildCmpt from "../../../view/cmpt/build/BuildCmpt";
import { ActionNode } from "../../passenger/ActionTree";
import PassengerModel from "../../passenger/PassengerModel";
import { TimeStateData } from "../../passenger/StateDataType";
import { StateType } from "../../passenger/StateEnum";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";

export enum EnginePowerObjState {
    IDLE, //投喂阶段
    START, //准备工作
    WORK, //工作阶段
    OUTPUT, //产出阶段
    HAPPY_START,
    HAPPY_END,
    ANGRY_START,
    ANGRY_END,
}

export enum EnginePowerObjWorkState {
    NORMAL,
    HAPPY,
    ANGRY
}

const ActionCfg = {
    [EnginePowerObjState.IDLE]: [10, 15], //投喂阶段
    [EnginePowerObjState.WORK]: [10, 15], //工作阶段
}

export default class EnginePowerObj extends BuildObj {

    public state: StateObj<EnginePowerObjState> = null

    public builds: BuildObj[] = [] //当前提供能量的设施

    private genOutputTime: number = 0

    public initObj() {
        this.actionTree.start(this.loop)
        return this
    }

    public isWork() {
        return this.state?.type == EnginePowerObjState.WORK
    }

    public getWorkSurplusTime() {
        if (!this.isWork()) return 0
        return this.state.data.timeData.getSurplusTime()
    }

    public isIdle() {
        return this.state?.type == EnginePowerObjState.IDLE
    }

    public getIdleSurplusTime() {
        if (!this.isIdle()) return 0
        return this.state.data.timeData.getSurplusTime()
    }

    private async loop(action: ActionNode) {
        await action.run(this.idle)
        await action.run(this.checkRoles)
        const WorkState = [
            // { type: EnginePowerObjWorkState.NORMAL, weight: 20 },
        ]
        let roles = this.carriage?.getPassengers() || []
        roles = roles.filter(role => role.actionAgent?.getState(StateType.IDLE))
        if (roles.length != 0) {
            WorkState.push({ type: EnginePowerObjWorkState.HAPPY, weight: 40 })
            WorkState.push({ type: EnginePowerObjWorkState.ANGRY, weight: 40 })
        }

        let idx = gameHelper.randomByWeight(WorkState)
        const target = WorkState[idx]
        switch (target.type) {
            case EnginePowerObjWorkState.NORMAL:
                await action.run(this.runNormal)
                break
            case EnginePowerObjWorkState.HAPPY:
                await action.run(this.runHappy)
                break
            case EnginePowerObjWorkState.ANGRY:
                await action.run(this.runAngry)
                break
        }
    }

    private async runHappy(action: ActionNode) {
        let anim = "aniHappy_1"
        let timeData = new TimeStateData().init(this.getAnimTime(anim))
        this.setState(EnginePowerObjState.HAPPY_START, { anim })
        await action.wait(timeData)

        anim = "aniHappy_2"
        timeData = new TimeStateData().init(this.getAnimTime(anim))
        this.setState(EnginePowerObjState.HAPPY_END, { anim })
        await action.wait(timeData)
        action.ok()
    }

    private async runAngry(action: ActionNode) {
        let anim = "aniAngry_1"
        let timeData = new TimeStateData().init(this.getAnimTime(anim))
        this.setState(EnginePowerObjState.ANGRY_START, { anim })
        await action.wait(timeData)

        anim = "aniAngry_2"
        timeData = new TimeStateData().init(this.getAnimTime(anim))
        this.setState(EnginePowerObjState.ANGRY_END, { anim })
        await action.wait(timeData)

        action.ok()
    }


    private async runNormal(action: ActionNode) {
        await action.run(this.start)
        await action.run(this.work)
        await action.run(this.doOutput)
        action.ok()
    }

    private checkRoles(action: ActionNode) {
        let roles = this.carriage?.getPassengers() || []
        if (roles.length > 0) {
            if (!roles.some(r => r.actionAgent?.getState(StateType.WANT_SLEEP))) {
                action.ok()
            }
        }
    }

    private async idle(action: ActionNode) {
        let anim = "aniIdle"
        let time = this.randomLoopAnimTime(ActionCfg[EnginePowerObjState.IDLE], anim)
        let timeData = new TimeStateData().init(time)
        this.setState(EnginePowerObjState.IDLE, { timeData, anim })
        await action.wait(timeData)
        action.ok()
    }

    private async start(action: ActionNode) {
        let anim = "aniStar"
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(EnginePowerObjState.START, { timeData, anim })
        await action.wait(timeData)
        action.ok()
    }

    private async work(action: ActionNode) {
        this.genOutputTime = gameHelper.world.getTime()
        let anim = "aniWork"
        let time = this.randomLoopAnimTime(ActionCfg[EnginePowerObjState.WORK], anim)
        let timeData = new TimeStateData().init(time)
        this.setState(EnginePowerObjState.WORK, { timeData, anim })
        await action.wait(timeData)

        if (this.checkOutput()) {
            action.ok()
        }
    }

    private checkOutput() {
        if (this.carriage.electricOutputObj.getOutputByPlay() <= 0) {
            return false
        }
        let intervalTime = ut.Time.Hour
        return Math.floor(gameHelper.world.getTime() / intervalTime) - Math.floor(this.genOutputTime / intervalTime) > 0
    }

    private async doOutput(action: ActionNode) {
        let anim = "aniOutput"
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(EnginePowerObjState.OUTPUT, { timeData, anim })
        ut.wait(time * 0.2).then(() => {
            let outputObj = this.carriage.electricOutputObj
            let output = outputObj.genOutputByPlay()
            let count = ut.random(Math.min(10, output), Math.min(20, output))
            ut.numAvgSplit(output, count).forEach((val) => {
                outputObj.addDrop(val, this)
            })
        })
        await action.wait(timeData)
        action.ok()
    }

    public addPower(build: BuildObj) {
        if (!this.builds.has(build)) {
            this.builds.push(build)
        }
    }

    private setState(type?, data?) {
        this.state = new StateObj<EnginePowerObjState>().init(type, data)
    }

    public getPower() {
        return this.builds.length
    }

    update(dt: any): void {
        this.actionTree && this.actionTree.update(dt)
    }
}